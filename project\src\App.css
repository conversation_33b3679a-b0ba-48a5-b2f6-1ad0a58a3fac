.app {
  text-align: center;
  padding: 2rem;
  font-family: <PERSON><PERSON>, sans-serif;
}

.app h1 {
  color: #333;
  margin-bottom: 2rem;
}

.canvas-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
  perspective: 1000px; /* Enables 3D perspective for the flip effect */
}

.canvas {
  border: 2px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.6s ease-in-out;
  transform-style: preserve-3d;
}

.canvas:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.canvas.flipped {
  transform: scaleX(-1); /* Horizontal flip */
}

/* Alternative flip effects - you can uncomment these and comment out the scaleX(-1) above */

/* Vertical flip */
/* .canvas.flipped {
  transform: scaleY(-1);
} */

/* 3D flip around Y-axis */
/* .canvas.flipped {
  transform: rotateY(180deg);
} */

/* 3D flip around X-axis */
/* .canvas.flipped {
  transform: rotateX(180deg);
} */

.app p {
  color: #666;
  margin: 1rem 0;
  font-size: 1.1rem;
}