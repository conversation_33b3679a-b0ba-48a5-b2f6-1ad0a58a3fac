import { useRef, useEffect, useState } from "react";
import "./App.css";

function App() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isFlipped, setIsFlipped] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size
    canvas.width = 400;
    canvas.height = 300;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw some content on the canvas
    drawCanvasContent(ctx, canvas.width, canvas.height);
  }, []);

  const drawCanvasContent = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    // Draw background
    ctx.fillStyle = "#f0f0f0";
    ctx.fillRect(0, 0, width, height);

    // Draw a simple shape
    ctx.fillStyle = "#4CAF50";
    ctx.fillRect(50, 50, 100, 80);

    // Draw some text
    ctx.fillStyle = "#333";
    ctx.font = "20px Arial";
    ctx.fillText("Click to Flip!", 200, 100);

    // Draw a circle
    ctx.fillStyle = "#FF5722";
    ctx.beginPath();
    ctx.arc(300, 200, 40, 0, 2 * Math.PI);
    ctx.fill();

    // Draw an arrow pointing right
    ctx.fillStyle = "#2196F3";
    ctx.beginPath();
    ctx.moveTo(100, 200);
    ctx.lineTo(150, 180);
    ctx.lineTo(150, 195);
    ctx.lineTo(180, 195);
    ctx.lineTo(180, 205);
    ctx.lineTo(150, 205);
    ctx.lineTo(150, 220);
    ctx.closePath();
    ctx.fill();
  };

  const handleCanvasClick = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <div className="app">
      <h1>Flippable Canvas Demo</h1>
      <div className="canvas-container">
        <canvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          className={`canvas ${isFlipped ? "flipped" : ""}`}
          style={{ cursor: "pointer" }}
        />
      </div>
      <p>Click the canvas to flip it horizontally!</p>
      <p>Current state: {isFlipped ? "Flipped" : "Normal"}</p>
    </div>
  );
}

export default App;
